import Pricing from "@/components/blocks/pricing";
import { getPricingPage } from "@/services/page";
import Pricing from 'types/blocks/pricing.d'
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getPricingPage(locale);
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/`;
  }
  return {
    title: {
      template: `%s`,
      default: page.Pricing.title || "",
    },
    description: page.Pricing.description || "",
        alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function PricingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getPricingPage(locale);

  return <>{page.pricing && <Pricing pricing={page.pricing} />}</>;
}
