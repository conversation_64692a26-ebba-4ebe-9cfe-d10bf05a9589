import { getUuid } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";
import {
  AIRequest,
  AIResponse,
  GRSAITextRequest,
  GRSAIImageRequest,
  GRSAIFluxRequest,
  GRSAIVideoRequest,
  GRSAITextResponse,
  GRSAIImageResponse,
  GRSAIVideoResponse,
  GRSAIFluxResponse,
  GRSAIResultResponse
} from "@/types/ai-model";
import {
  getActiveAIModelById,
  calculateModelCost,
  createAIModelUsage,
  updateAIModelUsage
} from "@/models/ai-model";

/**
 * GRSAI 提供商配置
 */
const GRSAI_CONFIG = {
  baseURL: {
    overseas: 'https://api.grsai.com',
    domestic: 'https://grsai.dakka.com.cn'
  },
  defaultRegion: 'overseas',
  timeout: 60000,
  retryAttempts: 3,
  retryDelay: 1000
};

/**
 * GRSAI API 客户端
 */
export class GRSAIProvider {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey?: string, region: 'overseas' | 'domestic' = 'overseas') {
    this.apiKey = apiKey || process.env.GRSAI_APIKEY || '';
    this.baseURL = GRSAI_CONFIG.baseURL[region];
    
    if (!this.apiKey) {
      throw new Error('GRSAI API key is required');
    }
  }

  /**
   * 发送HTTP请求
   */
  private async makeRequest(
    endpoint: string,
    data: any,
    options: { stream?: boolean } = {}
  ): Promise<any> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`
    };

    const requestOptions: RequestInit = {
      method: 'POST',
      headers,
      body: JSON.stringify(data)
    };

    console.log(`[GRSAI] Making request to: ${url}`);
    console.log(`[GRSAI] Request data:`, JSON.stringify(data, null, 2));

    let attempt = 0;
    while (attempt < GRSAI_CONFIG.retryAttempts) {
      try {
        const response = await fetch(url, requestOptions);

        console.log(`[GRSAI] Response status: ${response.status}`);
        console.log(`[GRSAI] Response headers:`, Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
          const errorText = await response.text();
          console.log(`[GRSAI] Error response:`, errorText);
          throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
        }

        if (options.stream) {
          return response; // 返回流响应
        }

        // 检查响应内容类型
        const contentType = response.headers.get('content-type');
        console.log(`[GRSAI] Content-Type: ${contentType}`);

        if (contentType && contentType.includes('text/plain')) {
          // 处理SSE格式的响应
          const text = await response.text();
          console.log(`[GRSAI] SSE response text:`, text);
          return this.parseSSEResponse(text);
        }

        const jsonResponse = await response.json();
        console.log(`[GRSAI] JSON response:`, JSON.stringify(jsonResponse, null, 2));
        return jsonResponse;
      } catch (error) {
        console.log(`[GRSAI] Request attempt ${attempt + 1} failed:`, error);
        attempt++;
        if (attempt >= GRSAI_CONFIG.retryAttempts) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, GRSAI_CONFIG.retryDelay * attempt));
      }
    }
  }

  /**
   * 解析SSE格式的响应
   */
  private parseSSEResponse(text: string): any {
    try {
      console.log(`[GRSAI] Parsing SSE response, total length: ${text.length}`);

      // 查找最后一个完整的JSON数据
      const lines = text.split('\n');
      let lastJsonData = null;
      let validDataCount = 0;

      for (const line of lines) {
        if (line.startsWith('data: ') && line !== 'data: [DONE]') {
          try {
            const jsonStr = line.substring(6); // 移除 "data: " 前缀
            console.log(`[GRSAI] Parsing JSON line: ${jsonStr}`);
            lastJsonData = JSON.parse(jsonStr);
            validDataCount++;
            console.log(`[GRSAI] Successfully parsed JSON data #${validDataCount}:`, lastJsonData);
          } catch (e) {
            console.log(`[GRSAI] Failed to parse JSON line: ${line}`, e);
          }
        }
      }

      console.log(`[GRSAI] Found ${validDataCount} valid JSON data entries`);
      console.log(`[GRSAI] Final parsed data:`, lastJsonData);

      return lastJsonData || { error: 'No valid JSON data found in SSE response' };
    } catch (error) {
      console.log(`[GRSAI] SSE parsing error:`, error);
      throw new Error(`Failed to parse SSE response: ${error}`);
    }
  }

  /**
   * 文本生成
   */
  async generateText(request: GRSAITextRequest): Promise<GRSAITextResponse | ReadableStream> {
    const endpoint = '/v1/chat/completions';
    return await this.makeRequest(endpoint, request, { stream: request.stream });
  }

  /**
   * 图像生成 (Sora/GPT-4o)
   */
  async generateImage(request: GRSAIImageRequest): Promise<GRSAIImageResponse> {
    const endpoint = '/v1/draw/completions';
    // 对于异步任务，设置webHook为-1以获取任务ID
    const requestWithWebhook = { ...request, webHook: request.webHook || '-1' };
    return await this.makeRequest(endpoint, requestWithWebhook);
  }

  /**
   * Flux 图像生成
   */
  async generateFluxImage(request: GRSAIFluxRequest): Promise<GRSAIFluxResponse> {
    const endpoint = '/v1/draw/flux';
    // 对于异步任务，设置webHook为-1以获取任务ID
    const requestWithWebhook = { ...request, webHook: request.webHook || '-1' };
    return await this.makeRequest(endpoint, requestWithWebhook);
  }

  /**
   * 视频生成
   */
  async generateVideo(request: GRSAIVideoRequest): Promise<GRSAIVideoResponse> {
    const endpoint = '/v1/video/veo';
    // 对于异步任务，设置webHook为-1以获取任务ID
    const requestWithWebhook = { ...request, webHook: request.webHook || '-1' };
    return await this.makeRequest(endpoint, requestWithWebhook);
  }

  /**
   * 查询结果
   */
  async getResult(id: string): Promise<GRSAIResultResponse> {
    const endpoint = '/v1/draw/result';
    return await this.makeRequest(endpoint, { id });
  }
}

/**
 * 服务接口
 */
export class GrsAIService {
  private grsaiProvider: GRSAIProvider;

  constructor() {
    this.grsaiProvider = new GRSAIProvider();
  }

  /**
   * 处理AI请求
   */
  async processRequestFromClient(
    userUuid: string,
    request: AIRequest
  ): Promise<AIResponse> {
    const requestId = getUuid();
    const taskId = getUuid();

    // 获取模型配置
    const model = await getActiveAIModelById(request.model);
    if (!model) {
      throw new Error(`Model ${request.model} not found`);
    }

    // 估算成本
    const estimatedCost = await this.estimateCost(request, model);

    // 创建使用记录
    const usage = await createAIModelUsage({
      user_uuid: userUuid,
      model_id: request.model,
      request_id: requestId,
      task_id: taskId,
      provider: 'grsai',
      credits_consumed: estimatedCost,
      status: 'pending',
      request_params: request,
      started_at: getIsoTimestr()
    });

    try {
      let response: AIResponse;

      switch (request.type) {
        case 'text':
          response = await this.handleTextGeneration(request, requestId, taskId);
          break;
        case 'image':
          response = await this.handleImageGeneration(request, requestId, taskId);
          break;
        case 'video':
          response = await this.handleVideoGeneration(request, requestId, taskId);
          break;
        default:
          throw new Error(`Unsupported request type: ${request.type}`);
      }

      // 更新使用记录（只有在非pending状态时才更新为完成）
      if (response.status !== 'pending') {
        await updateAIModelUsage(taskId, {
          status: response.status === 'success' ? 'success' : 'failed',
          output_size: this.calculateOutputSize(response),
          response_data: response,
          completed_at: getIsoTimestr()
        });
      }

      return response;
    } catch (error) {
      // 更新失败记录
      await updateAIModelUsage(taskId, {
        status: 'failed',
        error_reason: 'error',
        error_detail: error instanceof Error ? error.message : 'Unknown error',
        completed_at: getIsoTimestr()
      });

      throw error;
    }
  }

  /**
   * 处理文本生成
   */
  private async handleTextGeneration(request: AIRequest, requestId: string, taskId: string): Promise<AIResponse> {
    // 检查是否有上传的图片，如果有则构建多模态消息
    const uploadedImages = request.options?.uploadedImages || [];

    let messages: Array<{ role: 'system' | 'user' | 'assistant'; content: any }>;

    if (uploadedImages.length > 0) {
      // 多模态消息格式
      const content: any[] = [
        { type: 'text', text: request.prompt }
      ];

      // 添加图片
      uploadedImages.forEach(imageUrl => {
        content.push({
          type: 'image_url',
          image_url: { url: imageUrl }
        });
      });

      messages = request.options?.messages?.map(msg => ({
        role: msg.role as 'system' | 'user' | 'assistant',
        content: msg.image_url ? [
          { type: 'text', text: msg.content },
          { type: 'image_url', image_url: { url: msg.image_url } }
        ] : msg.content
      })) || [{ role: 'user' as const, content }];
    } else {
      // 纯文本消息格式
      messages = request.options?.messages?.map(msg => ({
        role: msg.role as 'system' | 'user' | 'assistant',
        content: msg.content
      })) || [{ role: 'user' as const, content: request.prompt }];
    }

    const grsaiRequest: GRSAITextRequest = {
      model: request.model,
      messages,
      stream: request.options?.stream || false,
      temperature: request.options?.temperature,
      max_tokens: request.options?.max_tokens
    };

    const response = await this.grsaiProvider.generateText(grsaiRequest);

    if (response instanceof ReadableStream) {
      // 处理流式响应
      return {
        id: requestId,
        task_id: taskId,
        type: 'text',
        status: 'pending',
        result: { text: '' } // 流式响应需要客户端处理
      };
    } else {
      const textResponse = response as GRSAITextResponse;
      return {
        id: requestId,
        task_id: taskId,
        type: 'text',
        status: 'success',
        result: {
          text: textResponse.choices[0]?.message?.content || ''
        },
        usage: {
          input_tokens: this.estimateTokens(request.prompt),
          output_tokens: this.estimateTokens(textResponse.choices[0]?.message?.content || ''),
          total_tokens: 0,
          credits_consumed: 0 // 将在后续计算
        }
      };
    }
  }

  /**
   * 处理图像生成
   */
  private async handleImageGeneration(request: AIRequest, requestId: string, taskId: string): Promise<AIResponse> {
    console.log(`[AI Service] Starting image generation for model: ${request.model}`);

    const model = await getActiveAIModelById(request.model);
    if (!model) {
      throw new Error(`Model ${request.model} not found`);
    }

    console.log(`[AI Service] Model config:`, model);

    // 合并参考图片和上传图片
    const allReferenceImages = [
      ...(request.options?.referenceImages || []),
      ...(request.options?.uploadedImages || [])
    ];

    console.log(`[AI Service] Reference images:`, request.options?.referenceImages);
    console.log(`[AI Service] Uploaded images:`, request.options?.uploadedImages);
    console.log(`[AI Service] All reference images:`, allReferenceImages);

    let response: any;

    if (model.api_endpoint === '/v1/draw/flux') {
      // Flux 模型
      const grsaiRequest: GRSAIFluxRequest = {
        model: request.model,
        prompt: request.prompt,
        urls: allReferenceImages.length > 0 ? allReferenceImages : undefined,
        seed: request.options?.seed,
        aspectRatio: request.options?.aspectRatio || request.options?.size,
        webHook: '-1', // 强制使用轮询模式
        cdn: request.options?.cdn || 'global'
      };

      console.log(`[AI Service] Calling Flux API with request:`, grsaiRequest);
      response = await this.grsaiProvider.generateFluxImage(grsaiRequest);
    } else {
      // Sora/GPT-4o 模型
      const grsaiRequest: GRSAIImageRequest = {
        model: request.model,
        prompt: request.prompt,
        size: request.options?.size,
        variants: request.options?.variants,
        urls: allReferenceImages.length > 0 ? allReferenceImages : undefined,
        webHook: '-1', // 强制使用轮询模式
        cdn: request.options?.cdn || 'global'
      };

      console.log(`[AI Service] Calling Image API with request:`, grsaiRequest);
      response = await this.grsaiProvider.generateImage(grsaiRequest);
    }

    console.log(`[AI Service] GRSAI response:`, response);

    // 检查是否是任务创建响应（包含code和data.id）
    if (response.code === 0 && response.data && response.data.id) {
      console.log(`[AI Service] Task created with ID: ${response.data.id}`);

      // 更新使用记录，保存GRSAI任务ID
      await updateAIModelUsage(taskId, {
        external_request_id: response.data.id as string,
        response_data: response,
        status: 'pending'
      });

      // 这是任务创建响应，返回pending状态
      return {
        id: requestId,
        task_id: taskId,
        type: 'image',
        status: 'pending',
        progress: 0,
        usage: {
          credits_consumed: model.credits_per_unit
        }
      };
    }

    console.log(`[AI Service] Direct result response, status: ${response.status}`);
    // 直接返回结果的情况
    return {
      id: requestId,
      task_id: taskId,
      type: 'image',
      status: response.status === 'succeeded' ? 'success' :
              response.status === 'failed' ? 'failed' : 'running',
      progress: response.progress || 0,
      result: {
        images: response.status === 'succeeded' && response.url ? [
          {
            url: response.url,
            width: response.width || 1024,
            height: response.height || 1024
          }
        ] : undefined
      },
      error: response.status === 'failed' ? {
        reason: response.failure_reason || 'error',
        detail: response.error || 'Unknown error'
      } : undefined,
      usage: {
        credits_consumed: model.credits_per_unit
      }
    };
  }

  /**
   * 处理视频生成
   */
  private async handleVideoGeneration(request: AIRequest, requestId: string, taskId: string): Promise<AIResponse> {
    // 优先使用上传的图片作为首帧，如果没有则使用指定的首帧URL
    const firstFrameUrl = request.options?.uploadedImages?.[0] || request.options?.firstFrameUrl;

    const grsaiRequest: GRSAIVideoRequest = {
      model: request.model,
      prompt: request.prompt,
      firstFrameUrl,
      webHook: '-1', // 强制使用轮询模式
      cdn: request.options?.cdn || 'global'
    };

    const response = await this.grsaiProvider.generateVideo(grsaiRequest);
    const model = await getActiveAIModelById(request.model);

    // 检查是否是任务创建响应（包含code和data.id）
    if ('code' in response && 'data' in response && response.code === 0 && response.data && typeof response.data === 'object' && response.data !== null && 'id' in response.data) {
      // 这是任务创建响应，返回pending状态
      return {
        id: requestId,
        task_id: taskId,
        type: 'video',
        status: 'pending',
        progress: 0,
        usage: {
          credits_consumed: model?.credits_per_unit || 0
        }
      };
    }

    // 直接返回结果的情况
    return {
      id: requestId,
      task_id: taskId,
      type: 'video',
      status: response.status === 'succeeded' ? 'success' :
              response.status === 'failed' ? 'failed' : 'running',
      progress: response.progress || 0,
      result: {
        video: response.status === 'succeeded' && response.url ? {
          url: response.url
        } : undefined
      },
      error: response.status === 'failed' ? {
        reason: response.failure_reason || 'error',
        detail: response.error || 'Unknown error'
      } : undefined,
      usage: {
        credits_consumed: model?.credits_per_unit || 0
      }
    };
  }

  /**
   * 估算请求成本
   */
  private async estimateCost(request: AIRequest, model: any): Promise<number> {
    switch (request.type) {
      case 'text':
        const inputTokens = this.estimateTokens(request.prompt);
        const maxTokens = request.options?.max_tokens || 1000;
        return await calculateModelCost(request.model, inputTokens, maxTokens);
      case 'image':
        const variants = request.options?.variants || 1;
        return model.credits_per_unit * variants;
      case 'video':
        return model.credits_per_unit;
      default:
        return model.credits_per_unit;
    }
  }

  /**
   * 估算token数量
   */
  private estimateTokens(text: string): number {
    // 简单估算：中文按字符数，英文按单词数 * 1.3
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').split(/\s+/).filter(w => w.length > 0).length;
    return Math.ceil(chineseChars + englishWords * 1.3);
  }

  /**
   * 计算输出大小
   */
  private calculateOutputSize(response: AIResponse): number {
    switch (response.type) {
      case 'text':
        return this.estimateTokens(response.result?.text || '');
      case 'image':
        return response.result?.images?.length || 0;
      case 'video':
        return response.result?.video ? 1 : 0;
      default:
        return 0;
    }
  }
}
