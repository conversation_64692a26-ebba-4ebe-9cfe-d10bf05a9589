-- 添加external_request_id字段到ai_model_usage表
-- 用于存储外部提供商（GRSAI、Replicate等）的任务ID

-- 添加字段
ALTER TABLE ai_model_usage ADD COLUMN IF NOT EXISTS external_request_id VARCHAR(255);

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_ai_model_usage_external_request ON ai_model_usage(external_request_id);

-- 添加字段注释
COMMENT ON COLUMN ai_model_usage.external_request_id IS '外部提供商的任务ID (GRSAI/Replicate等)';

-- 迁移现有数据：从response_data中提取外部ID
UPDATE ai_model_usage 
SET external_request_id = 
  CASE 
    WHEN response_data->>'data' IS NOT NULL AND response_data->'data'->>'id' IS NOT NULL 
    THEN response_data->'data'->>'id'
    WHEN response_data->>'id' IS NOT NULL 
    THEN response_data->>'id'
    ELSE NULL
  END
WHERE external_request_id IS NULL 
  AND response_data IS NOT NULL 
  AND status = 'pending';
